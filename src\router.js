// 导入路由相关函数
import { createRouter, createWebHashHistory } from "vue-router";
/*
导入以下子组件:
CheckGroup.vue, CheckItem.vue, CheckSet.vue, MyAdmin.vue, CheckBook.vue, CheckData.vue, MyLogin.vue
*/
import UserLogin from "./components/UserLogin.vue"
import HomeIndex from "./components/HomeIndex.vue"
import MyPage from "./components/MyPage.vue";
import ShowMe from "./components/ShowMe.vue";
import PersonalCenter from "./components/PersonalCenter.vue";
import UpdatePwd from "./components/UpdatePwd.vue";

// 财务管理相关组件
import Dashboard from "./components/Dashboard.vue";
import IncomeRecord from "./components/IncomeRecord.vue";
import ExpenseRecord from "./components/ExpenseRecord.vue";
import CategoryManagement from "./components/CategoryManagement.vue";
import FamilyMembers from "./components/FamilyMembers.vue";
import Accounts from "./components/Accounts.vue";
import Reports from "./components/Reports.vue";

/*创建路由对象数组*/
const routes = [
  {
    path: "/",
    component: UserLogin,
  },
  {
    path: "/admin",
    name: "admin",
    component: HomeIndex,
    children: [
      // 原有页面
      { path: "MyPage", component: MyPage },
      { path: "ShowMe", component: ShowMe },
      { path: "PersonalCenter", component: PersonalCenter },
      { path: "UpdatePwd", component: UpdatePwd },

      // 财务管理页面
      { path: "Dashboard", component: Dashboard },
      { path: "IncomeRecord", component: IncomeRecord },
      { path: "ExpenseRecord", component: ExpenseRecord },
      { path: "Category", component: CategoryManagement },
      { path: "FamilyMembers", component: FamilyMembers },
      { path: "Accounts", component: Accounts },
      { path: "Budget", component: MyPage }, // 临时使用MyPage
      { path: "Reports", component: Reports },
      { path: "Trends", component: MyPage }, // 临时使用MyPage
      { path: "MemberStats", component: MyPage }, // 临时使用MyPage
    ],
  },
];

/*创建路由实例对象*/
const router = createRouter({
  history: createWebHashHistory(),
  routes: routes,
});

/*导出路由对象 */
export default router;
