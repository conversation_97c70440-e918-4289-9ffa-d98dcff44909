<template>
    <div id="showme">
        <h3 id="headline">展示页面 - 滚动测试</h3>
        <hr />
        <p>这是一个展示页面，用于测试滚动功能</p>

        <!-- 测试内容区域 -->
        <div class="test-content">
            <div class="content-section" v-for="i in 20" :key="i">
                <h4>测试区块 {{ i }}</h4>
                <p>这是第 {{ i }} 个测试区块的内容。这个区块包含了一些文本内容，用于测试页面的滚动功能是否正常工作。</p>
                <div class="test-card">
                    <h5>卡片标题 {{ i }}</h5>
                    <p>这是卡片内容，包含了一些描述性文字。当页面内容超出视口高度时，应该能够正常滚动查看所有内容。</p>
                    <ul>
                        <li>列表项 1 - 测试项目</li>
                        <li>列表项 2 - 功能验证</li>
                        <li>列表项 3 - 滚动测试</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="footer-content">
            <p>页面底部内容 - 如果您能看到这里，说明滚动功能正常工作！</p>
        </div>
    </div>
</template>
<script>
export default {
    name: 'ShowMe',
}
</script>
<style>
#showme {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    overflow-y: auto;
    box-sizing: border-box;
    padding: 20px;
}

/* 滚动条样式 */
#showme::-webkit-scrollbar {
    width: 6px;
}

#showme::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

#showme::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

#showme::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.3);
}
#headline {
    text-align: left;
    margin-top: 0;
    color: #2c3e50;
    font-size: 24px;
    font-weight: 600;
}

.test-content {
    margin-top: 20px;
}

.content-section {
    margin-bottom: 30px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.content-section h4 {
    color: #409eff;
    margin: 0 0 15px 0;
    font-size: 18px;
    font-weight: 600;
}

.content-section p {
    color: #606266;
    line-height: 1.6;
    margin: 0 0 15px 0;
}

.test-card {
    background: rgba(255, 255, 255, 0.8);
    padding: 15px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    margin-top: 15px;
}

.test-card h5 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-size: 16px;
    font-weight: 600;
}

.test-card p {
    color: #7f8c8d;
    font-size: 14px;
    margin: 0 0 10px 0;
}

.test-card ul {
    margin: 0;
    padding-left: 20px;
}

.test-card li {
    color: #606266;
    margin-bottom: 5px;
    font-size: 14px;
}

.footer-content {
    margin-top: 40px;
    padding: 20px;
    background: rgba(82, 196, 26, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(82, 196, 26, 0.2);
    text-align: center;
}

.footer-content p {
    color: #52c41a;
    font-weight: 600;
    margin: 0;
    font-size: 16px;
}
</style>