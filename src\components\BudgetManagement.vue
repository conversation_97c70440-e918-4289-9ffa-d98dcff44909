<template>
  <div class="budget-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon class="title-icon"><TrendCharts /></el-icon>
        预算管理
      </h2>
      <p class="page-subtitle">设置和监控家庭预算，合理规划支出</p>
    </div>

    <!-- 预算概览 -->
    <div class="budget-overview">
      <div class="overview-grid">
        <div class="overview-card total">
          <div class="card-icon">
            <el-icon><Wallet /></el-icon>
          </div>
          <div class="card-content">
            <h3>总预算</h3>
            <p class="amount">¥{{ totalBudget.toLocaleString() }}</p>
            <span class="period">{{ currentPeriod }}</span>
          </div>
        </div>

        <div class="overview-card used">
          <div class="card-icon">
            <el-icon><Money /></el-icon>
          </div>
          <div class="card-content">
            <h3>已使用</h3>
            <p class="amount">¥{{ usedBudget.toLocaleString() }}</p>
            <span class="usage-rate" :class="usageRateClass">{{ usageRate }}%</span>
          </div>
        </div>

        <div class="overview-card remaining">
          <div class="card-icon">
            <el-icon><CreditCard /></el-icon>
          </div>
          <div class="card-content">
            <h3>剩余预算</h3>
            <p class="amount" :class="remainingBudget >= 0 ? 'positive' : 'negative'">
              ¥{{ remainingBudget.toLocaleString() }}
            </p>
            <span class="days-left">剩余{{ daysLeft }}天</span>
          </div>
        </div>

        <div class="overview-card daily">
          <div class="card-icon">
            <el-icon><Calendar /></el-icon>
          </div>
          <div class="card-content">
            <h3>日均可用</h3>
            <p class="amount">¥{{ dailyAvailable.toLocaleString() }}</p>
            <span class="suggestion">{{ budgetSuggestion }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 预算设置和监控 -->
    <div class="budget-content">
      <div class="content-grid">
        <!-- 预算设置 -->
        <div class="budget-settings">
          <el-card class="settings-card">
            <template #header>
              <div class="card-header">
                <span>预算设置</span>
                <el-button type="primary" size="small" @click="showBudgetDialog = true" icon="Plus">
                  新增预算
                </el-button>
              </div>
            </template>

            <div class="budget-list">
              <div
                v-for="budget in budgetList"
                :key="budget.id"
                class="budget-item"
                :class="{ 'over-budget': budget.usageRate > 100 }"
              >
                <div class="budget-header">
                  <div class="budget-info">
                    <div class="budget-icon" :style="{ backgroundColor: budget.color }">
                      <el-icon><component :is="budget.icon" /></el-icon>
                    </div>
                    <div class="budget-details">
                      <h4 class="budget-name">{{ budget.name }}</h4>
                      <p class="budget-period">{{ budget.period }} · {{ budget.member || '全部成员' }}</p>
                    </div>
                  </div>
                  <div class="budget-actions">
                    <el-dropdown @command="handleBudgetAction">
                      <el-button type="text" icon="MoreFilled" />
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item :command="{action: 'edit', budget}">编辑</el-dropdown-item>
                          <el-dropdown-item :command="{action: 'copy', budget}">复制</el-dropdown-item>
                          <el-dropdown-item :command="{action: 'delete', budget}" divided>删除</el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </div>

                <div class="budget-progress">
                  <div class="progress-info">
                    <span class="used-amount">¥{{ budget.used.toLocaleString() }}</span>
                    <span class="total-amount">/ ¥{{ budget.amount.toLocaleString() }}</span>
                    <span class="usage-percent" :class="getUsageClass(budget.usageRate)">
                      {{ budget.usageRate }}%
                    </span>
                  </div>
                  <el-progress
                    :percentage="Math.min(budget.usageRate, 100)"
                    :color="getProgressColor(budget.usageRate)"
                    :stroke-width="8"
                    :show-text="false"
                  />
                  <div class="budget-status">
                    <span v-if="budget.usageRate > 100" class="over-budget-text">
                      超支 ¥{{ (budget.used - budget.amount).toLocaleString() }}
                    </span>
                    <span v-else class="remaining-text">
                      剩余 ¥{{ (budget.amount - budget.used).toLocaleString() }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 预算分析 -->
        <div class="budget-analysis">
          <el-card class="analysis-card">
            <template #header>
              <div class="card-header">
                <span>预算分析</span>
                <el-select v-model="analysisType" size="small" style="width: 120px">
                  <el-option label="本月" value="month" />
                  <el-option label="本季度" value="quarter" />
                  <el-option label="本年" value="year" />
                </el-select>
              </div>
            </template>

            <div class="analysis-content">
              <!-- 预算执行趋势 -->
              <div class="trend-section">
                <h4>预算执行趋势</h4>
                <div class="trend-chart">
                  <div class="chart-placeholder">
                    <el-icon class="chart-icon"><TrendCharts /></el-icon>
                    <p>预算执行趋势图</p>
                    <p class="chart-desc">显示{{ getAnalysisPeriod() }}的预算使用情况</p>
                  </div>
                </div>
              </div>

              <!-- 分类预算对比 -->
              <div class="category-comparison">
                <h4>分类预算对比</h4>
                <div class="comparison-list">
                  <div
                    v-for="category in categoryBudgets"
                    :key="category.name"
                    class="comparison-item"
                  >
                    <div class="category-info">
                      <span class="category-name">{{ category.name }}</span>
                      <span class="category-ratio">{{ category.ratio }}%</span>
                    </div>
                    <div class="category-bars">
                      <div class="budget-bar">
                        <div class="bar-label">预算</div>
                        <div class="bar-container">
                          <div
                            class="bar-fill budget"
                            :style="{ width: category.budgetPercent + '%' }"
                          ></div>
                        </div>
                        <div class="bar-value">¥{{ category.budget.toLocaleString() }}</div>
                      </div>
                      <div class="actual-bar">
                        <div class="bar-label">实际</div>
                        <div class="bar-container">
                          <div
                            class="bar-fill actual"
                            :style="{ width: category.actualPercent + '%' }"
                          ></div>
                        </div>
                        <div class="bar-value">¥{{ category.actual.toLocaleString() }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 预算提醒 -->
    <div class="budget-alerts">
      <el-card class="alerts-card">
        <template #header>
          <div class="card-header">
            <span>预算提醒</span>
            <el-button type="text" size="small" @click="markAllRead">全部已读</el-button>
          </div>
        </template>

        <div class="alerts-list">
          <div
            v-for="alert in budgetAlerts"
            :key="alert.id"
            class="alert-item"
            :class="alert.type"
          >
            <div class="alert-icon">
              <el-icon v-if="alert.type === 'warning'"><Warning /></el-icon>
              <el-icon v-else-if="alert.type === 'danger'"><CircleCloseFilled /></el-icon>
              <el-icon v-else><InfoFilled /></el-icon>
            </div>
            <div class="alert-content">
              <p class="alert-title">{{ alert.title }}</p>
              <p class="alert-message">{{ alert.message }}</p>
              <p class="alert-time">{{ formatTime(alert.time) }}</p>
            </div>
            <div class="alert-actions">
              <el-button type="text" size="small" @click="dismissAlert(alert.id)">忽略</el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 新增/编辑预算对话框 -->
    <el-dialog
      v-model="showBudgetDialog"
      :title="editingBudget ? '编辑预算' : '新增预算'"
      width="500px"
      @close="resetBudgetForm"
    >
      <el-form
        ref="budgetFormRef"
        :model="budgetForm"
        :rules="budgetRules"
        label-width="80px"
      >
        <el-form-item label="预算名称" prop="name">
          <el-input v-model="budgetForm.name" placeholder="请输入预算名称" />
        </el-form-item>

        <el-form-item label="预算金额" prop="amount">
          <el-input-number
            v-model="budgetForm.amount"
            :min="0"
            :precision="2"
            controls-position="right"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="预算周期" prop="period">
          <el-select v-model="budgetForm.period" style="width: 100%">
            <el-option label="每月" value="monthly" />
            <el-option label="每季度" value="quarterly" />
            <el-option label="每年" value="yearly" />
          </el-select>
        </el-form-item>

        <el-form-item label="适用分类" prop="category">
          <el-select v-model="budgetForm.category" style="width: 100%">
            <el-option label="全部分类" value="all" />
            <el-option label="餐饮" value="餐饮" />
            <el-option label="交通" value="交通" />
            <el-option label="购物" value="购物" />
            <el-option label="娱乐" value="娱乐" />
            <el-option label="医疗" value="医疗" />
            <el-option label="教育" value="教育" />
          </el-select>
        </el-form-item>

        <el-form-item label="适用成员" prop="member">
          <el-select v-model="budgetForm.member" style="width: 100%">
            <el-option label="全部成员" value="all" />
            <el-option label="张三" value="张三" />
            <el-option label="李四" value="李四" />
            <el-option label="张小明" value="张小明" />
            <el-option label="张奶奶" value="张奶奶" />
          </el-select>
        </el-form-item>

        <el-form-item label="预警阈值" prop="alertThreshold">
          <el-slider
            v-model="budgetForm.alertThreshold"
            :min="50"
            :max="100"
            :step="5"
            show-stops
            show-input
            :format-tooltip="formatTooltip"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showBudgetDialog = false">取消</el-button>
          <el-button type="primary" @click="saveBudget" :loading="saving">
            {{ editingBudget ? '更新' : '创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import dayjs from 'dayjs'
import {
  TrendCharts,
  Wallet,
  Money,
  CreditCard,
  Calendar,
  Plus,
  MoreFilled,
  Warning,
  CircleCloseFilled,
  InfoFilled
} from '@element-plus/icons-vue'

export default {
  name: 'BudgetManagement',
  components: {
    TrendCharts,
    Wallet,
    Money,
    CreditCard,
    Calendar,
    Plus,
    MoreFilled,
    Warning,
    CircleCloseFilled,
    InfoFilled
  },
  setup() {
    const showBudgetDialog = ref(false)
    const editingBudget = ref(null)
    const saving = ref(false)
    const budgetFormRef = ref(null)
    const analysisType = ref('month')

    // 预算概览数据
    const totalBudget = ref(15000)
    const usedBudget = ref(8600)
    const currentPeriod = ref('2024年6月')
    const daysLeft = ref(12)

    const remainingBudget = computed(() => totalBudget.value - usedBudget.value)
    const usageRate = computed(() => Math.round((usedBudget.value / totalBudget.value) * 100))
    const dailyAvailable = computed(() => Math.round(remainingBudget.value / daysLeft.value))
    
    const usageRateClass = computed(() => {
      if (usageRate.value >= 90) return 'danger'
      if (usageRate.value >= 70) return 'warning'
      return 'normal'
    })

    const budgetSuggestion = computed(() => {
      if (usageRate.value > 100) return '已超支'
      if (usageRate.value > 80) return '需节制'
      return '正常范围'
    })

    // 预算表单
    const budgetForm = reactive({
      name: '',
      amount: 0,
      period: 'monthly',
      category: 'all',
      member: 'all',
      alertThreshold: 80
    })

    // 表单验证规则
    const budgetRules = {
      name: [
        { required: true, message: '请输入预算名称', trigger: 'blur' }
      ],
      amount: [
        { required: true, message: '请输入预算金额', trigger: 'blur' },
        { type: 'number', min: 0.01, message: '预算金额必须大于0', trigger: 'blur' }
      ],
      period: [
        { required: true, message: '请选择预算周期', trigger: 'change' }
      ],
      category: [
        { required: true, message: '请选择适用分类', trigger: 'change' }
      ]
    }

    // 预算列表数据
    const budgetList = reactive([
      {
        id: 1,
        name: '餐饮预算',
        amount: 3000,
        used: 2100,
        usageRate: 70,
        period: '每月',
        category: '餐饮',
        member: '全部成员',
        color: '#ff6b6b',
        icon: 'Food'
      },
      {
        id: 2,
        name: '交通预算',
        amount: 1500,
        used: 1680,
        usageRate: 112,
        period: '每月',
        category: '交通',
        member: '全部成员',
        color: '#4ecdc4',
        icon: 'Car'
      },
      {
        id: 3,
        name: '购物预算',
        amount: 2000,
        used: 1200,
        usageRate: 60,
        period: '每月',
        category: '购物',
        member: '全部成员',
        color: '#45b7d1',
        icon: 'ShoppingBag'
      },
      {
        id: 4,
        name: '娱乐预算',
        amount: 800,
        used: 650,
        usageRate: 81,
        period: '每月',
        category: '娱乐',
        member: '全部成员',
        color: '#96ceb4',
        icon: 'VideoPlay'
      }
    ])

    // 分类预算对比数据
    const categoryBudgets = reactive([
      {
        name: '餐饮',
        budget: 3000,
        actual: 2100,
        budgetPercent: 100,
        actualPercent: 70,
        ratio: 70
      },
      {
        name: '交通',
        budget: 1500,
        actual: 1680,
        budgetPercent: 50,
        actualPercent: 56,
        ratio: 112
      },
      {
        name: '购物',
        budget: 2000,
        actual: 1200,
        budgetPercent: 67,
        actualPercent: 40,
        ratio: 60
      },
      {
        name: '娱乐',
        budget: 800,
        actual: 650,
        budgetPercent: 27,
        actualPercent: 22,
        ratio: 81
      }
    ])

    // 预算提醒数据
    const budgetAlerts = reactive([
      {
        id: 1,
        type: 'danger',
        title: '交通预算超支',
        message: '本月交通支出已超出预算12%，建议调整出行方式',
        time: new Date(Date.now() - 2 * 60 * 60 * 1000)
      },
      {
        id: 2,
        type: 'warning',
        title: '娱乐预算预警',
        message: '娱乐支出已达到预算的81%，请注意控制',
        time: new Date(Date.now() - 6 * 60 * 60 * 1000)
      },
      {
        id: 3,
        type: 'info',
        title: '购物预算正常',
        message: '购物支出控制良好，仅使用了60%的预算',
        time: new Date(Date.now() - 24 * 60 * 60 * 1000)
      }
    ])

    return {
      showBudgetDialog,
      editingBudget,
      saving,
      budgetFormRef,
      analysisType,
      totalBudget,
      usedBudget,
      currentPeriod,
      daysLeft,
      remainingBudget,
      usageRate,
      dailyAvailable,
      usageRateClass,
      budgetSuggestion,
      budgetForm,
      budgetRules,
      budgetList,
      categoryBudgets,
      budgetAlerts
    }
  },
  methods: {
    // 获取使用率样式类
    getUsageClass(rate) {
      if (rate >= 100) return 'over-budget'
      if (rate >= 80) return 'warning'
      return 'normal'
    },

    // 获取进度条颜色
    getProgressColor(rate) {
      if (rate >= 100) return '#f56c6c'
      if (rate >= 80) return '#e6a23c'
      return '#67c23a'
    },

    // 获取分析周期标签
    getAnalysisPeriod() {
      const periods = {
        month: '本月',
        quarter: '本季度',
        year: '本年'
      }
      return periods[this.analysisType] || '本月'
    },

    // 处理预算操作
    handleBudgetAction(command) {
      const { action, budget } = command
      switch (action) {
        case 'edit':
          this.editBudget(budget)
          break
        case 'copy':
          this.copyBudget(budget)
          break
        case 'delete':
          this.deleteBudget(budget)
          break
      }
    },

    // 编辑预算
    editBudget(budget) {
      this.editingBudget = budget
      Object.assign(this.budgetForm, {
        name: budget.name,
        amount: budget.amount,
        period: budget.period === '每月' ? 'monthly' : budget.period === '每季度' ? 'quarterly' : 'yearly',
        category: budget.category === '全部分类' ? 'all' : budget.category,
        member: budget.member === '全部成员' ? 'all' : budget.member,
        alertThreshold: 80
      })
      this.showBudgetDialog = true
    },

    // 复制预算
    copyBudget(budget) {
      const newBudget = {
        ...budget,
        id: Date.now(),
        name: budget.name + ' (副本)',
        used: 0,
        usageRate: 0
      }
      this.budgetList.push(newBudget)
      this.$message.success('预算复制成功')
    },

    // 删除预算
    deleteBudget(budget) {
      this.$confirm('确定要删除这个预算吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.budgetList.findIndex(b => b.id === budget.id)
        if (index > -1) {
          this.budgetList.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },

    // 保存预算
    async saveBudget() {
      try {
        const valid = await this.$refs.budgetFormRef.validate()
        if (!valid) return

        this.saving = true

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        if (this.editingBudget) {
          // 更新现有预算
          Object.assign(this.editingBudget, {
            name: this.budgetForm.name,
            amount: this.budgetForm.amount,
            period: this.budgetForm.period === 'monthly' ? '每月' : this.budgetForm.period === 'quarterly' ? '每季度' : '每年',
            category: this.budgetForm.category === 'all' ? '全部分类' : this.budgetForm.category,
            member: this.budgetForm.member === 'all' ? '全部成员' : this.budgetForm.member
          })
          this.$message.success('预算更新成功')
        } else {
          // 创建新预算
          const newBudget = {
            id: Date.now(),
            name: this.budgetForm.name,
            amount: this.budgetForm.amount,
            used: 0,
            usageRate: 0,
            period: this.budgetForm.period === 'monthly' ? '每月' : this.budgetForm.period === 'quarterly' ? '每季度' : '每年',
            category: this.budgetForm.category === 'all' ? '全部分类' : this.budgetForm.category,
            member: this.budgetForm.member === 'all' ? '全部成员' : this.budgetForm.member,
            color: '#409eff',
            icon: 'Money'
          }
          this.budgetList.push(newBudget)
          this.$message.success('预算创建成功')
        }

        this.showBudgetDialog = false
        this.resetBudgetForm()
      } catch (error) {
        this.$message.error('保存失败，请重试')
      } finally {
        this.saving = false
      }
    },

    // 重置预算表单
    resetBudgetForm() {
      this.editingBudget = null
      Object.assign(this.budgetForm, {
        name: '',
        amount: 0,
        period: 'monthly',
        category: 'all',
        member: 'all',
        alertThreshold: 80
      })
      this.$refs.budgetFormRef?.clearValidate()
    },

    // 格式化提示
    formatTooltip(value) {
      return `${value}%`
    },

    // 格式化时间
    formatTime(time) {
      return dayjs(time).format('MM-DD HH:mm')
    },

    // 忽略提醒
    dismissAlert(alertId) {
      const index = this.budgetAlerts.findIndex(alert => alert.id === alertId)
      if (index > -1) {
        this.budgetAlerts.splice(index, 1)
      }
    },

    // 标记全部已读
    markAllRead() {
      this.budgetAlerts.splice(0)
      this.$message.success('已标记全部提醒为已读')
    }
  }
}
</script>

<style scoped>
.budget-management {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  overflow-y: auto;
}

/* 页面标题 */
.page-header {
  margin-bottom: 24px;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.title-icon {
  margin-right: 12px;
  color: #409eff;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 预算概览 */
.budget-overview {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.overview-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.overview-card.total .card-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.overview-card.used .card-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.overview-card.remaining .card-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.overview-card.daily .card-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-content h3 {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.card-content .amount {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.card-content .amount.positive {
  color: #27ae60;
}

.card-content .amount.negative {
  color: #e74c3c;
}

.period, .days-left, .suggestion {
  font-size: 12px;
  color: #95a5a6;
}

.usage-rate {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 4px;
}

.usage-rate.normal {
  background: #d5f4e6;
  color: #27ae60;
}

.usage-rate.warning {
  background: #fef0e6;
  color: #e67e22;
}

.usage-rate.danger {
  background: #fdeaea;
  color: #e74c3c;
}

/* 内容网格 */
.budget-content {
  margin-bottom: 24px;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
}

/* 预算设置卡片 */
.settings-card, .analysis-card, .alerts-card {
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

/* 预算列表 */
.budget-list {
  max-height: 600px;
  overflow-y: auto;
}

.budget-item {
  padding: 20px;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  margin-bottom: 16px;
  background: #fafbfc;
  transition: all 0.2s ease;
}

.budget-item:hover {
  background: #f5f7fa;
  border-color: #409eff;
}

.budget-item.over-budget {
  border-color: #f56c6c;
  background: #fef0f0;
}

.budget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.budget-info {
  display: flex;
  align-items: center;
}

.budget-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
  font-size: 18px;
}

.budget-details h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #2c3e50;
}

.budget-period {
  font-size: 12px;
  color: #7f8c8d;
  margin: 0;
}

/* 预算进度 */
.budget-progress {
  margin-top: 12px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.used-amount {
  color: #2c3e50;
  font-weight: 600;
}

.total-amount {
  color: #7f8c8d;
}

.usage-percent {
  font-weight: 600;
}

.usage-percent.normal {
  color: #27ae60;
}

.usage-percent.warning {
  color: #e67e22;
}

.usage-percent.over-budget {
  color: #e74c3c;
}

.budget-status {
  margin-top: 8px;
  font-size: 12px;
}

.over-budget-text {
  color: #e74c3c;
  font-weight: 600;
}

.remaining-text {
  color: #27ae60;
}

/* 预算分析 */
.analysis-content {
  padding: 0;
}

.trend-section {
  margin-bottom: 32px;
}

.trend-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #2c3e50;
}

.trend-chart {
  height: 200px;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
}

.chart-placeholder {
  text-align: center;
  color: #999;
}

.chart-icon {
  font-size: 48px;
  margin-bottom: 12px;
  color: #d9d9d9;
}

.chart-desc {
  font-size: 12px;
  margin: 4px 0 0 0;
}

/* 分类预算对比 */
.category-comparison h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #2c3e50;
}

.comparison-item {
  margin-bottom: 20px;
}

.category-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.category-name {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
}

.category-ratio {
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 600;
}

.budget-bar, .actual-bar {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.bar-label {
  width: 40px;
  font-size: 12px;
  color: #7f8c8d;
  text-align: right;
  margin-right: 8px;
}

.bar-container {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 8px;
}

.bar-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.bar-fill.budget {
  background: #409eff;
}

.bar-fill.actual {
  background: #67c23a;
}

.bar-value {
  width: 80px;
  font-size: 12px;
  color: #2c3e50;
  text-align: right;
}

/* 预算提醒 */
.alerts-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 12px;
  border-left: 4px solid;
}

.alert-item.info {
  background: #f0f9ff;
  border-left-color: #409eff;
}

.alert-item.warning {
  background: #fdf6ec;
  border-left-color: #e6a23c;
}

.alert-item.danger {
  background: #fef0f0;
  border-left-color: #f56c6c;
}

.alert-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.alert-item.info .alert-icon {
  color: #409eff;
}

.alert-item.warning .alert-icon {
  color: #e6a23c;
}

.alert-item.danger .alert-icon {
  color: #f56c6c;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.alert-message {
  font-size: 13px;
  color: #7f8c8d;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.alert-time {
  font-size: 12px;
  color: #bdc3c7;
  margin: 0;
}

.alert-actions {
  margin-left: 12px;
}

/* 对话框样式 */
.dialog-footer {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .budget-management {
    padding: 16px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .overview-card {
    padding: 16px;
  }

  .card-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .card-content .amount {
    font-size: 20px;
  }

  .budget-item {
    padding: 16px;
  }

  .budget-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

/* 滚动条样式 */
.budget-list::-webkit-scrollbar,
.alerts-list::-webkit-scrollbar {
  width: 6px;
}

.budget-list::-webkit-scrollbar-track,
.alerts-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.budget-list::-webkit-scrollbar-thumb,
.alerts-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.budget-list::-webkit-scrollbar-thumb:hover,
.alerts-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
