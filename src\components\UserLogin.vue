<template>
  <div class="container">
    <!-- 登录 -->
    <div class="formbox login">
      <form class="form" id="form2" :model="Loginform">
        <h2 class="title">登录</h2>
        <input type="email" placeholder="邮箱" class="input" v-model="Loginform.email"/>
        <input type="password" placeholder="密码" class="input" v-model="Loginform.password"/>
        <a href="#" class="link">忘记密码？</a>
        <button class="btn" type="button" @click="login">登录</button>
      </form>
    </div>
    <!-- 注册 -->
    <div class="formbox register">
      <form class="form" id="form1">
        <h2 class="title">注册</h2>
        <input type="text" class="input" placeholder="用户名" />
        <input type="email" class="input" placeholder="邮箱" />
        <input type="password" class="input" placeholder="密码" />
        <button class="btn" type="button" @click="register">注册</button>
      </form>
    </div>
    <!-- 浮层 -->
    <div class="overlay-box">
      <div class="overlay">
        <div class="panel overlay-left">
          <button class="btn" id="signIn">已有账号？立即登录</button>
        </div>
        <div class="panel overlay-right">
          <button class="btn" id="signUp">没有账号？立即注册</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { onMounted } from 'vue';

export default {
  name: 'UserLogin',
  setup() {
    onMounted(() => {
      const signInBtn = document.querySelector('#signIn');
      const signUpBtn = document.querySelector('#signUp');
      const container = document.querySelector('.container');
      
      if (signInBtn && signUpBtn && container) {
        signInBtn.addEventListener('click', () => {
          container.classList.remove('panel-active');
        });
        signUpBtn.addEventListener('click', () => {
          container.classList.add('panel-active');
        });
      }
    });
  },
  data() {
    return {
      Loginform: {
        email: '',
        password: '',
      },
    };
  },
  methods: {
    login() {
      // 简单的前端验证，暂时不连接后端
      if (!this.Loginform.email || !this.Loginform.password) {
        this.$message.error('请输入用户名和密码');
        return;
      }

      // 模拟登录成功
      this.$message.success('登录成功');
      this.$router.push('/admin');
      // 保存到本地存储，不是会话存储
      localStorage.setItem('nickname', this.Loginform.email);

      // 如果需要连接后端，可以取消注释以下代码：
      /*
      var req = {
        username: this.Loginform.email,
        password: this.Loginform.password,
      }
      this.$axios.post("http://localhost:9857/user/login", req).then((res) => {
        console.log(res);
        this.$message.success('登录成功');
        this.$router.push('/admin');
        localStorage.setItem('nickname', this.Loginform.email);
      }).catch((error) => {
        console.error('登录失败:', error);
        this.$message.error('登录失败，请检查用户名和密码');
      });
      */
    },
    register() {
      console.log('register');
    },
    virify() {
      // 如果localStorage中有nickname，则跳转到首页
      if (localStorage.getItem('nickname')) {
        this.$router.push('admin/MyPage');
      }
    }
  },
  mounted() {
    this.virify();
  }
};
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
}
:global(body) {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  /* background-image: url('../assets/preview.jpg'); */
  background-size: cover;
  position: relative; /* 为绝对定位的子元素提供参照 */
}

.container {
  width: 80vw;            /* 宽度为视口宽度的80% */
  max-width: 758px;       /* 最大宽度限制 */
  aspect-ratio: 758 / 420;/* 保持原始的宽高比 */
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(255, 255, 255, 0.3);
}


.formbox {
  height: 100%;
  position: absolute;
  top: 0;
  transition: all 0.6s ease-in-out;
}
.login {
  left: 0;
  width: 50%;
  z-index: 2;
}
.register {
  left: 0;
  width: 50%;
  opacity: 0;
  z-index: 1;
}
.form {
  background-color: #e9e9e9;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0 3rem;
  height: 100%;
  text-align: center;
}
.title {
  font-weight: 300;
  margin: 0;
  margin-bottom: 1.25rem;
}
.input {
  background-color: #fff;
  border: none;
  padding: 0.9rem;
  margin: 0.5rem 0;
  width: 100%;
}
.link {
  color: #333;
  font-size: 16px;
  margin: 1.5rem 0;
  text-decoration: none;
}
.btn {
  background-color: #095c91;
  background-image: linear-gradient(0deg, #095c91 0%, #0393a3 74%);
  border-radius: 5px;
  border: none;
  color: #e9e9e9;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: bold;
  letter-spacing: 0.1rem;
  padding: 0.9rem 4rem;
  text-transform: uppercase;
  transition: transform 80ms ease-in;
}
.form > .btn {
  margin-top: 1.5rem;
}
.btn:active {
  transform: scale(0.95);
}
.overlay-box {
  height: 100%;
  left: 50%;
  overflow: hidden;
  position: absolute;
  top: 0;
  transition: transform 0.6s ease-in-out;
  width: 50%;
  z-index: 100;
}
.overlay {
  background-color: rgba(255, 255, 255, 0.25);
  height: 100%;
  left: -100%;
  position: relative;
  transform: translateX(0);
  transition: transform 0.6s ease-in-out;
  width: 200%;
}
.panel {
  width: 50%;
  height: 100%;
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: absolute;
  text-align: center;
  top: 0;
  transform: translateX(0);
  transition: transform 0.6s ease-in-out;
}
.overlay-left {
  transform: translateX(-20%);
}
.overlay-right {
  right: 0;
  transform: translateX(0);
}
.panel-active .login {
  transform: translateX(100%);
}
.panel-active .register {
  transform: translateX(100%);
  opacity: 1;
  z-index: 3;
}
.panel-active .overlay-left {
  transform: translateX(0);
}
.panel-active .overlay-box {
  transform: translateX(-100%);
}
.panel-active .overlay {
  transform: translateX(50%);
}
.panel-active .overlay-right {
  transform: translateX(20%);
}
</style>
