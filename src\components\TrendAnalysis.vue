<template>
  <div class="trend-analysis">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon class="title-icon"><TrendCharts /></el-icon>
        趋势分析
      </h2>
      <p class="page-subtitle">深度分析收支变化趋势，洞察财务规律</p>
    </div>

    <!-- 筛选控制 -->
    <div class="filter-section">
      <el-card class="filter-card">
        <el-form inline class="filter-form">
          <el-form-item label="时间范围">
            <el-select v-model="timeRange" @change="updateTrends" style="width: 150px">
              <el-option label="最近7天" value="7days" />
              <el-option label="最近30天" value="30days" />
              <el-option label="最近3个月" value="3months" />
              <el-option label="最近6个月" value="6months" />
              <el-option label="最近1年" value="1year" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="分析维度">
            <el-select v-model="analysisType" @change="updateTrends" style="width: 150px">
              <el-option label="按日统计" value="daily" />
              <el-option label="按周统计" value="weekly" />
              <el-option label="按月统计" value="monthly" />
              <el-option label="按季度统计" value="quarterly" />
            </el-select>
          </el-form-item>

          <el-form-item label="成员筛选">
            <el-select v-model="selectedMember" @change="updateTrends" style="width: 150px">
              <el-option label="全部成员" value="all" />
              <el-option label="张三" value="张三" />
              <el-option label="李四" value="李四" />
              <el-option label="张小明" value="张小明" />
              <el-option label="张奶奶" value="张奶奶" />
            </el-select>
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="exportTrends" icon="Download">导出趋势</el-button>
            <el-button @click="resetFilters" icon="Refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 趋势概览 -->
    <div class="trend-overview">
      <div class="overview-grid">
        <div class="trend-card income-trend">
          <div class="card-header">
            <div class="card-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <div class="card-info">
              <h3>收入趋势</h3>
              <p class="trend-value">¥{{ incomeTrend.current.toLocaleString() }}</p>
              <span class="trend-change" :class="incomeTrend.changeType">
                <el-icon v-if="incomeTrend.changeType === 'up'"><ArrowUp /></el-icon>
                <el-icon v-else-if="incomeTrend.changeType === 'down'"><ArrowDown /></el-icon>
                <el-icon v-else><Minus /></el-icon>
                {{ incomeTrend.changePercent }}%
              </span>
            </div>
          </div>
          <div class="mini-chart">
            <div class="chart-placeholder">
              <div class="trend-line income"></div>
            </div>
          </div>
        </div>

        <div class="trend-card expense-trend">
          <div class="card-header">
            <div class="card-icon">
              <el-icon><Money /></el-icon>
            </div>
            <div class="card-info">
              <h3>支出趋势</h3>
              <p class="trend-value">¥{{ expenseTrend.current.toLocaleString() }}</p>
              <span class="trend-change" :class="expenseTrend.changeType">
                <el-icon v-if="expenseTrend.changeType === 'up'"><ArrowUp /></el-icon>
                <el-icon v-else-if="expenseTrend.changeType === 'down'"><ArrowDown /></el-icon>
                <el-icon v-else><Minus /></el-icon>
                {{ expenseTrend.changePercent }}%
              </span>
            </div>
          </div>
          <div class="mini-chart">
            <div class="chart-placeholder">
              <div class="trend-line expense"></div>
            </div>
          </div>
        </div>

        <div class="trend-card balance-trend">
          <div class="card-header">
            <div class="card-icon">
              <el-icon><Wallet /></el-icon>
            </div>
            <div class="card-info">
              <h3>结余趋势</h3>
              <p class="trend-value" :class="balanceTrend.current >= 0 ? 'positive' : 'negative'">
                ¥{{ balanceTrend.current.toLocaleString() }}
              </p>
              <span class="trend-change" :class="balanceTrend.changeType">
                <el-icon v-if="balanceTrend.changeType === 'up'"><ArrowUp /></el-icon>
                <el-icon v-else-if="balanceTrend.changeType === 'down'"><ArrowDown /></el-icon>
                <el-icon v-else><Minus /></el-icon>
                {{ balanceTrend.changePercent }}%
              </span>
            </div>
          </div>
          <div class="mini-chart">
            <div class="chart-placeholder">
              <div class="trend-line balance"></div>
            </div>
          </div>
        </div>

        <div class="trend-card frequency-trend">
          <div class="card-header">
            <div class="card-icon">
              <el-icon><DataLine /></el-icon>
            </div>
            <div class="card-info">
              <h3>交易频次</h3>
              <p class="trend-value">{{ frequencyTrend.current }}笔</p>
              <span class="trend-change" :class="frequencyTrend.changeType">
                <el-icon v-if="frequencyTrend.changeType === 'up'"><ArrowUp /></el-icon>
                <el-icon v-else-if="frequencyTrend.changeType === 'down'"><ArrowDown /></el-icon>
                <el-icon v-else><Minus /></el-icon>
                {{ frequencyTrend.changePercent }}%
              </span>
            </div>
          </div>
          <div class="mini-chart">
            <div class="chart-placeholder">
              <div class="trend-line frequency"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要趋势图表 -->
    <div class="main-charts">
      <div class="charts-grid">
        <!-- 收支趋势对比图 -->
        <div class="chart-section full-width">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>收支趋势对比</span>
                <div class="chart-controls">
                  <el-radio-group v-model="chartViewType" size="small">
                    <el-radio-button label="line">折线图</el-radio-button>
                    <el-radio-button label="bar">柱状图</el-radio-button>
                    <el-radio-button label="area">面积图</el-radio-button>
                  </el-radio-group>
                  <el-button type="text" size="small" @click="refreshChart" icon="Refresh">刷新</el-button>
                </div>
              </div>
            </template>
            <div class="chart-container large">
              <canvas ref="mainTrendChart" id="mainTrendChart"></canvas>
            </div>
          </el-card>
        </div>

        <!-- 分类支出趋势 -->
        <div class="chart-section">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>分类支出趋势</span>
                <el-select v-model="selectedCategory" size="small" style="width: 120px">
                  <el-option label="全部分类" value="all" />
                  <el-option label="餐饮" value="餐饮" />
                  <el-option label="交通" value="交通" />
                  <el-option label="购物" value="购物" />
                  <el-option label="娱乐" value="娱乐" />
                  <el-option label="医疗" value="医疗" />
                  <el-option label="教育" value="教育" />
                </el-select>
              </div>
            </template>
            <div class="chart-container">
              <canvas ref="categoryTrendChart" id="categoryTrendChart"></canvas>
            </div>
          </el-card>
        </div>

        <!-- 成员消费趋势 -->
        <div class="chart-section">
          <el-card class="chart-card">
            <template #header>
              <div class="chart-header">
                <span>成员消费趋势</span>
                <el-button type="text" size="small" @click="viewMemberDetails" icon="View">详情</el-button>
              </div>
            </template>
            <div class="chart-container">
              <canvas ref="memberTrendChart" id="memberTrendChart"></canvas>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 趋势分析报告 -->
    <div class="trend-report">
      <el-card class="report-card">
        <template #header>
          <div class="card-header">
            <span>趋势分析报告</span>
            <el-button type="text" size="small" @click="generateReport" icon="Document">生成报告</el-button>
          </div>
        </template>
        
        <div class="report-content">
          <div class="report-grid">
            <!-- 关键发现 -->
            <div class="report-section">
              <h4>关键发现</h4>
              <div class="findings-list">
                <div
                  v-for="finding in keyFindings"
                  :key="finding.id"
                  class="finding-item"
                  :class="finding.type"
                >
                  <div class="finding-icon">
                    <el-icon v-if="finding.type === 'positive'"><SuccessFilled /></el-icon>
                    <el-icon v-else-if="finding.type === 'warning'"><WarningFilled /></el-icon>
                    <el-icon v-else><InfoFilled /></el-icon>
                  </div>
                  <div class="finding-content">
                    <p class="finding-title">{{ finding.title }}</p>
                    <p class="finding-desc">{{ finding.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 趋势预测 -->
            <div class="report-section">
              <h4>趋势预测</h4>
              <div class="predictions-list">
                <div
                  v-for="prediction in trendPredictions"
                  :key="prediction.id"
                  class="prediction-item"
                >
                  <div class="prediction-header">
                    <span class="prediction-title">{{ prediction.title }}</span>
                    <span class="prediction-confidence" :class="prediction.confidenceLevel">
                      {{ prediction.confidence }}% 置信度
                    </span>
                  </div>
                  <p class="prediction-desc">{{ prediction.description }}</p>
                  <div class="prediction-chart">
                    <div class="prediction-line" :class="prediction.trend"></div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 建议措施 -->
            <div class="report-section">
              <h4>建议措施</h4>
              <div class="suggestions-list">
                <div
                  v-for="suggestion in suggestions"
                  :key="suggestion.id"
                  class="suggestion-item"
                  :class="suggestion.priority"
                >
                  <div class="suggestion-header">
                    <span class="suggestion-title">{{ suggestion.title }}</span>
                    <el-tag :type="getSuggestionTagType(suggestion.priority)" size="small">
                      {{ suggestion.priority === 'high' ? '高优先级' : suggestion.priority === 'medium' ? '中优先级' : '低优先级' }}
                    </el-tag>
                  </div>
                  <p class="suggestion-desc">{{ suggestion.description }}</p>
                  <div class="suggestion-actions">
                    <el-button type="text" size="small" @click="applySuggestion(suggestion)">
                      采纳建议
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import {
  TrendCharts,
  Money,
  Wallet,
  DataLine,
  ArrowUp,
  ArrowDown,
  Minus,
  SuccessFilled,
  WarningFilled,
  InfoFilled
} from '@element-plus/icons-vue'

export default {
  name: 'TrendAnalysis',
  components: {
    TrendCharts,
    Money,
    Wallet,
    DataLine,
    ArrowUp,
    ArrowDown,
    Minus,
    SuccessFilled,
    WarningFilled,
    InfoFilled
  },
  data() {
    return {
      // 图表实例
      mainChartInstance: null,
      categoryChartInstance: null,
      memberChartInstance: null,

      // 图表状态管理
      isChartInitializing: false,
      chartQueue: []
    }
  },
  setup() {
    const timeRange = ref('30days')
    const analysisType = ref('daily')
    const selectedMember = ref('all')
    const selectedCategory = ref('all')
    const chartViewType = ref('line')

    // 图表引用
    const mainTrendChart = ref(null)
    const categoryTrendChart = ref(null)
    const memberTrendChart = ref(null)

    // 定时器引用
    const chartUpdateTimer = ref(null)
    const rangeUpdateTimer = ref(null)

    // 图表状态管理
    const isChartInitializing = ref(false)
    const chartQueue = ref([])

    // 趋势概览数据
    const incomeTrend = reactive({
      current: 12500,
      changePercent: 15.8,
      changeType: 'up'
    })

    const expenseTrend = reactive({
      current: 8600,
      changePercent: 8.3,
      changeType: 'up'
    })

    const balanceTrend = reactive({
      current: 3900,
      changePercent: 32.5,
      changeType: 'up'
    })

    const frequencyTrend = reactive({
      current: 45,
      changePercent: 12.5,
      changeType: 'down'
    })

    // 分类趋势数据
    const categoryTrends = reactive([
      {
        name: '餐饮',
        amount: 3200,
        changePercent: 12.5,
        changeType: 'up',
        color: '#ff6b6b',
        icon: 'Food'
      },
      {
        name: '交通',
        amount: 1800,
        changePercent: 5.2,
        changeType: 'down',
        color: '#4ecdc4',
        icon: 'Car'
      },
      {
        name: '购物',
        amount: 2100,
        changePercent: 18.7,
        changeType: 'up',
        color: '#45b7d1',
        icon: 'ShoppingBag'
      },
      {
        name: '娱乐',
        amount: 950,
        changePercent: 3.2,
        changeType: 'stable',
        color: '#96ceb4',
        icon: 'VideoPlay'
      },
      {
        name: '医疗',
        amount: 550,
        changePercent: 25.6,
        changeType: 'up',
        color: '#feca57',
        icon: 'Medicine'
      }
    ])

    // 成员趋势数据
    const memberTrends = reactive([
      {
        name: '张三',
        amount: 4200,
        changePercent: 8.5,
        changeType: 'up',
        percentage: 85,
        color: '#409eff'
      },
      {
        name: '李四',
        amount: 2800,
        changePercent: 12.3,
        changeType: 'up',
        percentage: 65,
        color: '#67c23a'
      },
      {
        name: '张小明',
        amount: 1200,
        changePercent: 15.8,
        changeType: 'down',
        percentage: 35,
        color: '#e6a23c'
      },
      {
        name: '张奶奶',
        amount: 400,
        changePercent: 2.1,
        changeType: 'stable',
        percentage: 20,
        color: '#f56c6c'
      }
    ])

    // 关键发现
    const keyFindings = reactive([
      {
        id: 1,
        type: 'positive',
        title: '收入稳步增长',
        description: '本月收入较上月增长15.8%，主要来源于工资和兼职收入的提升'
      },
      {
        id: 2,
        type: 'warning',
        title: '餐饮支出上升',
        description: '餐饮支出较上月增长12.5%，建议关注外出就餐频次'
      },
      {
        id: 3,
        type: 'info',
        title: '交易频次下降',
        description: '交易笔数较上月减少12.5%，可能与消费习惯改变有关'
      }
    ])

    // 趋势预测
    const trendPredictions = reactive([
      {
        id: 1,
        title: '下月收入预测',
        confidence: 85,
        confidenceLevel: 'high',
        trend: 'up',
        description: '基于历史数据分析，预计下月收入将继续保持增长趋势'
      },
      {
        id: 2,
        title: '支出控制效果',
        confidence: 72,
        confidenceLevel: 'medium',
        trend: 'stable',
        description: '支出控制措施开始显效，预计下月支出将趋于稳定'
      }
    ])

    // 建议措施
    const suggestions = reactive([
      {
        id: 1,
        title: '优化餐饮支出',
        priority: 'high',
        description: '建议制定每周外出就餐计划，控制餐饮支出在合理范围内'
      },
      {
        id: 2,
        title: '增加投资收入',
        priority: 'medium',
        description: '考虑将部分结余资金用于稳健投资，增加被动收入来源'
      },
      {
        id: 3,
        title: '建立应急基金',
        priority: 'medium',
        description: '建议预留3-6个月的生活费用作为应急基金'
      }
    ])

    // 生成图表数据
    const generateChartData = () => {
      // 主趋势图数据
      const labels = []
      const incomeData = []
      const expenseData = []
      const balanceData = []

      // 根据时间范围生成标签和数据
      const days = timeRange.value === '7days' ? 7 : timeRange.value === '30days' ? 30 : 90

      // 使用固定种子生成稳定的随机数
      let seed = 12345
      const seededRandom = () => {
        seed = (seed * 9301 + 49297) % 233280
        return seed / 233280
      }

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)
        labels.push(date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }))

        // 生成更稳定的模拟数据
        const baseIncome = 800
        const baseExpense = 500
        const variation = 0.4

        const incomeVariation = (seededRandom() - 0.5) * variation
        const expenseVariation = (seededRandom() - 0.5) * variation

        const income = Math.round(baseIncome * (1 + incomeVariation))
        const expense = Math.round(baseExpense * (1 + expenseVariation))

        incomeData.push(Math.max(income, 100)) // 确保最小值
        expenseData.push(Math.max(expense, 50))
        balanceData.push(Math.max(income, 100) - Math.max(expense, 50))
      }

      return { labels, incomeData, expenseData, balanceData }
    }

    return {
      timeRange,
      analysisType,
      selectedMember,
      selectedCategory,
      chartViewType,
      mainTrendChart,
      categoryTrendChart,
      memberTrendChart,
      chartUpdateTimer,
      rangeUpdateTimer,
      isChartInitializing,
      chartQueue,
      incomeTrend,
      expenseTrend,
      balanceTrend,
      frequencyTrend,
      categoryTrends,
      memberTrends,
      keyFindings,
      trendPredictions,
      suggestions,
      generateChartData
    }
  },
  methods: {
    // 安全销毁图表
    safeDestroyChart(chart) {
      if (chart && typeof chart.destroy === 'function') {
        try {
          chart.destroy()
        } catch (error) {
          console.warn('图表销毁时出现警告:', error)
        }
      }
    },

    // 检查是否可以初始化图表
    canInitializeChart() {
      return !this.isChartInitializing
    },

    // 获取时间范围标签
    getTimeRangeLabel() {
      const labels = {
        '7days': '最近7天',
        '30days': '最近30天',
        '3months': '最近3个月',
        '6months': '最近6个月',
        '1year': '最近1年'
      }
      return labels[this.timeRange] || '最近30天'
    },

    // 初始化主趋势图表
    async initMainTrendChart() {
      if (!this.$refs.mainTrendChart || !this.canInitializeChart()) return

      this.isChartInitializing = true

      try {
        // 销毁现有图表
        this.safeDestroyChart(this.mainChartInstance)
        this.mainChartInstance = null

      const ctx = this.$refs.mainTrendChart.getContext('2d')
      const { labels, incomeData, expenseData, balanceData } = this.generateChartData()

      // 根据图表类型设置配置
      let chartType = this.chartViewType
      let datasets = []

      if (chartType === 'area') {
        // 面积图使用line类型，但设置fill属性
        chartType = 'line'
        datasets = [
          {
            label: '收入',
            data: incomeData,
            borderColor: '#52c41a',
            backgroundColor: 'rgba(82, 196, 26, 0.2)',
            fill: true,
            tension: 0.4,
            borderWidth: 2,
            pointRadius: 3,
            pointHoverRadius: 5
          },
          {
            label: '支出',
            data: expenseData,
            borderColor: '#ff4d4f',
            backgroundColor: 'rgba(255, 77, 79, 0.2)',
            fill: true,
            tension: 0.4,
            borderWidth: 2,
            pointRadius: 3,
            pointHoverRadius: 5
          },
          {
            label: '结余',
            data: balanceData,
            borderColor: '#1890ff',
            backgroundColor: 'rgba(24, 144, 255, 0.2)',
            fill: true,
            tension: 0.4,
            borderWidth: 2,
            pointRadius: 3,
            pointHoverRadius: 5
          }
        ]
      } else if (chartType === 'line') {
        datasets = [
          {
            label: '收入',
            data: incomeData,
            borderColor: '#52c41a',
            backgroundColor: '#52c41a',
            fill: false,
            tension: 0.4,
            borderWidth: 3,
            pointRadius: 4,
            pointHoverRadius: 6
          },
          {
            label: '支出',
            data: expenseData,
            borderColor: '#ff4d4f',
            backgroundColor: '#ff4d4f',
            fill: false,
            tension: 0.4,
            borderWidth: 3,
            pointRadius: 4,
            pointHoverRadius: 6
          },
          {
            label: '结余',
            data: balanceData,
            borderColor: '#1890ff',
            backgroundColor: '#1890ff',
            fill: false,
            tension: 0.4,
            borderWidth: 3,
            pointRadius: 4,
            pointHoverRadius: 6
          }
        ]
      } else {
        // 柱状图
        datasets = [
          {
            label: '收入',
            data: incomeData,
            backgroundColor: 'rgba(82, 196, 26, 0.8)',
            borderColor: '#52c41a',
            borderWidth: 1
          },
          {
            label: '支出',
            data: expenseData,
            backgroundColor: 'rgba(255, 77, 79, 0.8)',
            borderColor: '#ff4d4f',
            borderWidth: 1
          },
          {
            label: '结余',
            data: balanceData,
            backgroundColor: 'rgba(24, 144, 255, 0.8)',
            borderColor: '#1890ff',
            borderWidth: 1
          }
        ]
      }

        // 等待一帧确保DOM完全渲染
        await this.$nextTick()

        this.mainChartInstance = new this.$Chart(ctx, {
          type: chartType,
          data: {
            labels: labels,
            datasets: datasets
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
              duration: 300, // 减少动画时间
              onComplete: () => {
                // 动画完成后标记初始化完成
                this.isChartInitializing = false
              }
            },
            plugins: {
              legend: {
                position: 'top',
                labels: {
                  usePointStyle: true,
                  padding: 20
                }
              },
              title: {
                display: true,
                text: `收支趋势对比 - ${this.getTimeRangeLabel()}`,
                font: {
                  size: 16,
                  weight: 'bold'
                }
              },
              tooltip: {
                mode: 'index',
                intersect: false,
                callbacks: {
                  label: function(context) {
                    return `${context.dataset.label}: ¥${context.parsed.y.toLocaleString()}`
                  }
                }
              }
            },
            scales: {
              x: {
                display: true,
                title: {
                  display: true,
                  text: '日期'
                }
              },
              y: {
                display: true,
                beginAtZero: true,
                title: {
                  display: true,
                  text: '金额 (¥)'
                },
                ticks: {
                  callback: function(value) {
                    return '¥' + value.toLocaleString()
                  }
                }
              }
            },
            interaction: {
              intersect: false,
              mode: 'index'
            },
            elements: {
              point: {
                hoverBackgroundColor: '#fff',
                hoverBorderWidth: 2
              }
            }
          }
        })
      } catch (error) {
        console.error('创建图表失败:', error)
        this.$message.error('图表创建失败，请刷新页面重试')
        this.isChartInitializing = false
      }
    },

    // 初始化分类趋势图表
    async initCategoryTrendChart() {
      if (!this.$refs.categoryTrendChart || !this.canInitializeChart()) return

      this.isChartInitializing = true

      try {
        // 销毁现有图表
        this.safeDestroyChart(this.categoryChartInstance)
        this.categoryChartInstance = null

        // 等待一帧确保DOM完全渲染
        await this.$nextTick()

      const ctx = this.$refs.categoryTrendChart.getContext('2d')

      const categoryData = [
        { name: '餐饮', value: 3200, color: '#ff6b6b' },
        { name: '交通', value: 1800, color: '#4ecdc4' },
        { name: '购物', value: 2100, color: '#45b7d1' },
        { name: '娱乐', value: 950, color: '#96ceb4' },
        { name: '医疗', value: 550, color: '#feca57' },
        { name: '教育', value: 1200, color: '#ff9ff3' }
      ]

        this.categoryChartInstance = new this.$Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: categoryData.map(item => item.name),
            datasets: [{
              data: categoryData.map(item => item.value),
              backgroundColor: categoryData.map(item => item.color),
              borderWidth: 2,
              borderColor: '#fff'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
              duration: 300,
              onComplete: () => {
                this.isChartInitializing = false
              }
            },
            plugins: {
              legend: {
                position: 'bottom',
              },
              title: {
                display: true,
                text: '分类支出分布'
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    const total = context.dataset.data.reduce((a, b) => a + b, 0)
                    const percentage = ((context.parsed / total) * 100).toFixed(1)
                    return `${context.label}: ¥${context.parsed.toLocaleString()} (${percentage}%)`
                  }
                }
              }
            }
          }
        })
      } catch (error) {
        console.error('创建分类图表失败:', error)
        this.$message.error('分类图表创建失败，请刷新页面重试')
        this.isChartInitializing = false
      }
    },

    // 初始化成员趋势图表
    async initMemberTrendChart() {
      if (!this.$refs.memberTrendChart || !this.canInitializeChart()) return

      this.isChartInitializing = true

      try {
        // 销毁现有图表
        this.safeDestroyChart(this.memberChartInstance)
        this.memberChartInstance = null

        // 等待一帧确保DOM完全渲染
        await this.$nextTick()

      const ctx = this.$refs.memberTrendChart.getContext('2d')

      const memberData = [
        { name: '张三', income: 12000, expense: 8600, color: '#409eff' },
        { name: '李四', income: 9500, expense: 6200, color: '#67c23a' },
        { name: '张小明', income: 800, expense: 2800, color: '#e6a23c' },
        { name: '张奶奶', income: 3000, expense: 1000, color: '#f56c6c' }
      ]

        this.memberChartInstance = new this.$Chart(ctx, {
          type: 'bar',
          data: {
            labels: memberData.map(item => item.name),
            datasets: [
              {
                label: '收入',
                data: memberData.map(item => item.income),
                backgroundColor: '#52c41a',
                borderColor: '#52c41a',
                borderWidth: 1
              },
              {
                label: '支出',
                data: memberData.map(item => item.expense),
                backgroundColor: '#ff4d4f',
                borderColor: '#ff4d4f',
                borderWidth: 1
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            animation: {
              duration: 300,
              onComplete: () => {
                this.isChartInitializing = false
              }
            },
            plugins: {
              legend: {
                position: 'top',
              },
              title: {
                display: true,
                text: '成员收支对比'
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    return `${context.dataset.label}: ¥${context.parsed.y.toLocaleString()}`
                  }
                }
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                ticks: {
                  callback: function(value) {
                    return '¥' + value.toLocaleString()
                  }
                }
              }
            }
          }
        })
      } catch (error) {
        console.error('创建成员图表失败:', error)
        this.$message.error('成员图表创建失败，请刷新页面重试')
        this.isChartInitializing = false
      }
    },

    // 更新趋势数据
    async updateTrends() {
      if (this.isChartInitializing) {
        console.log('图表正在初始化中，跳过更新')
        return
      }

      this.$message.info('正在更新趋势数据...')

      // 重新初始化图表
      await this.$nextTick()

      try {
        await Promise.all([
          this.initMainTrendChart(),
          this.initCategoryTrendChart(),
          this.initMemberTrendChart()
        ])
      } catch (error) {
        console.error('更新图表时出错:', error)
        this.$message.error('更新图表失败，请重试')
      }
    },

    // 重置筛选条件
    resetFilters() {
      this.timeRange = '30days'
      this.analysisType = 'daily'
      this.selectedMember = 'all'
      this.selectedCategory = 'all'
      this.updateTrends()
    },

    // 导出趋势数据
    exportTrends() {
      this.$message.info('导出功能开发中...')
    },

    // 刷新图表
    refreshChart() {
      this.$message.info('正在刷新图表...')
      this.updateTrends()
    },

    // 查看成员详情
    viewMemberDetails() {
      this.$router.push('/admin/Reports')
    },

    // 生成报告
    generateReport() {
      this.$message.info('正在生成趋势分析报告...')
    },

    // 获取建议标签类型
    getSuggestionTagType(priority) {
      const types = {
        'high': 'danger',
        'medium': 'warning',
        'low': 'info'
      }
      return types[priority] || 'info'
    },

    // 采纳建议
    applySuggestion(suggestion) {
      this.$confirm(`确定要采纳建议"${suggestion.title}"吗？`, '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        this.$message.success('建议已采纳，将为您制定相应的执行计划')
      }).catch(() => {})
    }
  },
  mounted() {
    // 组件挂载后初始化图表
    this.$nextTick(() => {
      this.initMainTrendChart()
      this.initCategoryTrendChart()
      this.initMemberTrendChart()
    })
  },
  beforeUnmount() {
    // 清理定时器
    if (this.chartUpdateTimer) {
      clearTimeout(this.chartUpdateTimer)
      this.chartUpdateTimer = null
    }
    if (this.rangeUpdateTimer) {
      clearTimeout(this.rangeUpdateTimer)
      this.rangeUpdateTimer = null
    }

    // 组件销毁前清理图表实例
    this.safeDestroyChart(this.mainChartInstance)
    this.mainChartInstance = null

    this.safeDestroyChart(this.categoryChartInstance)
    this.categoryChartInstance = null

    this.safeDestroyChart(this.memberChartInstance)
    this.memberChartInstance = null
  },
  watch: {
    // 监听图表类型变化
    chartViewType() {
      // 如果正在初始化，跳过更新
      if (this.isChartInitializing) {
        return
      }

      // 添加防抖，避免快速切换导致的问题
      if (this.chartUpdateTimer) {
        clearTimeout(this.chartUpdateTimer)
      }
      this.chartUpdateTimer = setTimeout(async () => {
        if (!this.isChartInitializing) {
          await this.$nextTick()
          await this.initMainTrendChart()
        }
      }, 150)
    },
    // 监听时间范围变化
    timeRange() {
      // 如果正在初始化，跳过更新
      if (this.isChartInitializing) {
        return
      }

      // 添加防抖，避免快速切换导致的问题
      if (this.rangeUpdateTimer) {
        clearTimeout(this.rangeUpdateTimer)
      }
      this.rangeUpdateTimer = setTimeout(async () => {
        if (!this.isChartInitializing) {
          await this.updateTrends()
        }
      }, 300)
    }
  }
}
</script>

<style scoped>
.trend-analysis {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  overflow-y: auto;
  box-sizing: border-box;
}

/* 页面标题 */
.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.title-icon {
  margin-right: 12px;
  color: #409eff;
  font-size: 32px;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 24px;
}

.filter-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.filter-form {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

/* 趋势概览 */
.trend-overview {
  margin-bottom: 24px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.trend-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.trend-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  color: white;
}

.income-trend .card-icon {
  background: linear-gradient(135deg, #52c41a, #73d13d);
}

.expense-trend .card-icon {
  background: linear-gradient(135deg, #ff4d4f, #ff7875);
}

.balance-trend .card-icon {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
}

.frequency-trend .card-icon {
  background: linear-gradient(135deg, #722ed1, #9254de);
}

.card-info h3 {
  font-size: 14px;
  color: #7f8c8d;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.trend-value {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.trend-value.positive {
  color: #52c41a;
}

.trend-value.negative {
  color: #ff4d4f;
}

.trend-change {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 600;
  gap: 4px;
}

.trend-change.up {
  color: #52c41a;
}

.trend-change.down {
  color: #ff4d4f;
}

.trend-change.stable {
  color: #faad14;
}

.mini-chart {
  height: 40px;
  margin-top: 12px;
}

.chart-placeholder {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.trend-line {
  width: 100%;
  height: 4px;
  border-radius: 2px;
  position: relative;
  overflow: hidden;
}

.trend-line::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 70%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5));
  animation: shimmer 2s infinite;
}

.trend-line.income {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.trend-line.expense {
  background: linear-gradient(90deg, #ff4d4f, #ff7875);
}

.trend-line.balance {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
}

.trend-line.frequency {
  background: linear-gradient(90deg, #722ed1, #9254de);
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 主要图表区域 */
.main-charts {
  margin-bottom: 24px;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.chart-section.full-width {
  grid-column: 1 / -1;
}

@media (min-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr 1fr;
  }
}

.chart-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #2c3e50;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.chart-container {
  height: 300px;
  padding: 16px;
  position: relative;
}

.chart-container.large {
  height: 400px;
}

.chart-container canvas {
  width: 100% !important;
  height: 100% !important;
}

.chart-icon {
  font-size: 48px;
  color: #d9d9d9;
  margin-bottom: 12px;
}

.chart-desc {
  font-size: 12px;
  color: #999;
  margin: 4px 0 16px 0;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #666;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.income {
  background: #52c41a;
}

.legend-color.expense {
  background: #ff4d4f;
}

.legend-color.balance {
  background: #1890ff;
}

/* 分类趋势 */
.category-trends {
  padding: 16px;
  max-height: 280px;
  overflow-y: auto;
}

.category-trend-item {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 8px;
  background: #fafafa;
  transition: background-color 0.2s ease;
}

.category-trend-item:hover {
  background: #f0f0f0;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.category-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  color: white;
  font-size: 14px;
}

.category-info {
  flex: 1;
}

.category-info h4 {
  font-size: 14px;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.category-amount {
  font-size: 12px;
  color: #7f8c8d;
  margin: 0;
}

.category-change {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 600;
  gap: 4px;
}

.category-change.up {
  color: #52c41a;
}

.category-change.down {
  color: #ff4d4f;
}

.category-change.stable {
  color: #faad14;
}

.category-chart {
  height: 6px;
  background: #e8e8e8;
  border-radius: 3px;
  overflow: hidden;
}

.mini-trend-line {
  height: 100%;
  width: 60%;
  border-radius: 3px;
  opacity: 0.8;
}

/* 成员趋势 */
.member-trends {
  padding: 16px;
  max-height: 280px;
  overflow-y: auto;
}

.member-trend-item {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 8px;
  background: #fafafa;
  transition: background-color 0.2s ease;
}

.member-trend-item:hover {
  background: #f0f0f0;
}

.member-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.member-avatar {
  margin-right: 12px;
}

.member-info {
  flex: 1;
}

.member-info h4 {
  font-size: 14px;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.member-amount {
  font-size: 12px;
  color: #7f8c8d;
  margin: 0;
}

.member-change {
  display: flex;
  align-items: center;
  font-size: 12px;
  font-weight: 600;
  gap: 4px;
}

.member-change.up {
  color: #52c41a;
}

.member-change.down {
  color: #ff4d4f;
}

.member-change.stable {
  color: #faad14;
}

.member-progress {
  margin-top: 8px;
}

/* 趋势报告 */
.trend-report {
  margin-bottom: 24px;
}

.report-card {
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.report-content {
  padding: 0;
}

.report-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

@media (min-width: 1200px) {
  .report-grid {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

.report-section h4 {
  font-size: 16px;
  color: #2c3e50;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

/* 关键发现 */
.findings-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.finding-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 12px;
}

.finding-item.positive {
  background: #f6ffed;
  border-left: 4px solid #52c41a;
}

.finding-item.warning {
  background: #fffbe6;
  border-left: 4px solid #faad14;
}

.finding-item.info {
  background: #f0f9ff;
  border-left: 4px solid #1890ff;
}

.finding-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.finding-item.positive .finding-icon {
  color: #52c41a;
}

.finding-item.warning .finding-icon {
  color: #faad14;
}

.finding-item.info .finding-icon {
  color: #1890ff;
}

.finding-content {
  flex: 1;
}

.finding-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.finding-desc {
  font-size: 12px;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.4;
}

/* 趋势预测 */
.predictions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.prediction-item {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  margin-bottom: 16px;
}

.prediction-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.prediction-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.prediction-confidence {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
}

.prediction-confidence.high {
  background: #f6ffed;
  color: #52c41a;
}

.prediction-confidence.medium {
  background: #fffbe6;
  color: #faad14;
}

.prediction-confidence.low {
  background: #fff2f0;
  color: #ff4d4f;
}

.prediction-desc {
  font-size: 12px;
  color: #7f8c8d;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.prediction-chart {
  height: 6px;
  background: #e8e8e8;
  border-radius: 3px;
  overflow: hidden;
}

.prediction-line {
  height: 100%;
  width: 75%;
  border-radius: 3px;
}

.prediction-line.up {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.prediction-line.down {
  background: linear-gradient(90deg, #ff4d4f, #ff7875);
}

.prediction-line.stable {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

/* 建议措施 */
.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.suggestion-item {
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
  border-left: 4px solid;
}

.suggestion-item.high {
  background: #fff2f0;
  border-left-color: #ff4d4f;
}

.suggestion-item.medium {
  background: #fffbe6;
  border-left-color: #faad14;
}

.suggestion-item.low {
  background: #f0f9ff;
  border-left-color: #1890ff;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.suggestion-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.suggestion-desc {
  font-size: 12px;
  color: #7f8c8d;
  margin: 0 0 12px 0;
  line-height: 1.4;
}

.suggestion-actions {
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .trend-analysis {
    padding: 16px;
  }

  .overview-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .report-grid {
    grid-template-columns: 1fr;
  }

  .filter-form {
    flex-direction: column;
    align-items: stretch;
  }

  .chart-controls {
    flex-direction: column;
    gap: 8px;
  }
}

/* 滚动条样式 */
.category-trends::-webkit-scrollbar,
.member-trends::-webkit-scrollbar {
  width: 6px;
}

.category-trends::-webkit-scrollbar-track,
.member-trends::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.category-trends::-webkit-scrollbar-thumb,
.member-trends::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.category-trends::-webkit-scrollbar-thumb:hover,
.member-trends::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
