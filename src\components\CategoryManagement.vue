<template>
  <div class="category-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2 class="page-title">
        <el-icon class="title-icon"><Grid /></el-icon>
        分类管理
      </h2>
      <p class="page-subtitle">管理收入和支出分类，让记账更有条理</p>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <el-button type="primary" @click="showAddDialog = true" icon="Plus">
        添加分类
      </el-button>
      <el-button @click="exportCategories" icon="Download">
        导出分类
      </el-button>
    </div>

    <!-- 分类标签页 -->
    <el-tabs v-model="activeTab" class="category-tabs">
      <!-- 支出分类 -->
      <el-tab-pane label="支出分类" name="expense">
        <div class="category-grid">
          <div
            v-for="category in expenseCategories"
            :key="category.id"
            class="category-card"
            :class="{ 'disabled': !category.enabled }"
          >
            <div class="category-header">
              <div class="category-icon" :style="{ backgroundColor: category.color }">
                <el-icon><component :is="category.icon" /></el-icon>
              </div>
              <div class="category-info">
                <h3 class="category-name">{{ category.name }}</h3>
                <p class="category-desc">{{ category.description }}</p>
              </div>
              <div class="category-actions">
                <el-dropdown @command="handleCategoryAction">
                  <el-button type="text" icon="MoreFilled" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'edit', category}">编辑</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'toggle', category}">
                        {{ category.enabled ? '禁用' : '启用' }}
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'delete', category}" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
            <div class="category-stats">
              <div class="stat-item">
                <span class="stat-label">本月使用</span>
                <span class="stat-value">{{ category.monthlyCount }}次</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">总金额</span>
                <span class="stat-value amount-expense">¥{{ category.totalAmount.toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 收入分类 -->
      <el-tab-pane label="收入分类" name="income">
        <div class="category-grid">
          <div
            v-for="category in incomeCategories"
            :key="category.id"
            class="category-card"
            :class="{ 'disabled': !category.enabled }"
          >
            <div class="category-header">
              <div class="category-icon" :style="{ backgroundColor: category.color }">
                <el-icon><component :is="category.icon" /></el-icon>
              </div>
              <div class="category-info">
                <h3 class="category-name">{{ category.name }}</h3>
                <p class="category-desc">{{ category.description }}</p>
              </div>
              <div class="category-actions">
                <el-dropdown @command="handleCategoryAction">
                  <el-button type="text" icon="MoreFilled" />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="{action: 'edit', category}">编辑</el-dropdown-item>
                      <el-dropdown-item :command="{action: 'toggle', category}">
                        {{ category.enabled ? '禁用' : '启用' }}
                      </el-dropdown-item>
                      <el-dropdown-item :command="{action: 'delete', category}" divided>删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>
            <div class="category-stats">
              <div class="stat-item">
                <span class="stat-label">本月使用</span>
                <span class="stat-value">{{ category.monthlyCount }}次</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">总金额</span>
                <span class="stat-value amount-income">¥{{ category.totalAmount.toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog
      v-model="showAddDialog"
      :title="editingCategory ? '编辑分类' : '添加分类'"
      width="500px"
      :center="true"
      :modal="true"
      :append-to-body="true"
      :lock-scroll="true"
      top="5vh"
      @close="resetCategoryForm"
    >
      <el-form
        :model="categoryForm"
        :rules="categoryRules"
        ref="categoryFormRef"
        label-width="100px"
      >
        <el-form-item label="分类类型" prop="type">
          <el-radio-group v-model="categoryForm.type">
            <el-radio value="expense">支出分类</el-radio>
            <el-radio value="income">收入分类</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" placeholder="请输入分类名称" />
        </el-form-item>

        <el-form-item label="分类描述" prop="description">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            placeholder="请输入分类描述"
            :rows="2"
          />
        </el-form-item>

        <el-form-item label="分类图标" prop="icon">
          <div class="icon-selector">
            <div
              v-for="iconName in availableIcons"
              :key="iconName"
              class="icon-option"
              :class="{ 'selected': categoryForm.icon === iconName }"
              @click="categoryForm.icon = iconName"
            >
              <el-icon><component :is="iconName" /></el-icon>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="分类颜色" prop="color">
          <div class="color-selector">
            <div
              v-for="color in availableColors"
              :key="color"
              class="color-option"
              :class="{ 'selected': categoryForm.color === color }"
              :style="{ backgroundColor: color }"
              @click="categoryForm.color = color"
            ></div>
          </div>
        </el-form-item>

        <el-form-item label="状态" prop="enabled">
          <el-switch v-model="categoryForm.enabled" active-text="启用" inactive-text="禁用" />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showAddDialog = false">取消</el-button>
        <el-button type="primary" @click="saveCategory" :loading="saving">
          {{ editingCategory ? '更新' : '添加' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import {
  Grid,
  Plus,
  Download,
  MoreFilled,
  Food,
  Car,
  ShoppingBag,
  VideoPlay,
  Medicine,
  Reading,
  House,
  Phone,
  Money,
  TrendCharts,
  Briefcase,
  Gift
} from '@element-plus/icons-vue'

export default {
  name: 'CategoryManagement',
  components: {
    Grid,
    Plus,
    Download,
    MoreFilled,
    Food,
    Car,
    ShoppingBag,
    VideoPlay,
    Medicine,
    Reading,
    House,
    Phone,
    Money,
    TrendCharts,
    Briefcase,
    Gift
  },
  setup() {
    const activeTab = ref('expense')
    const showAddDialog = ref(false)
    const editingCategory = ref(null)
    const saving = ref(false)
    const categoryFormRef = ref(null)

    // 分类表单
    const categoryForm = reactive({
      type: 'expense',
      name: '',
      description: '',
      icon: 'Food',
      color: '#ff6b6b',
      enabled: true
    })

    // 表单验证规则
    const categoryRules = {
      name: [
        { required: true, message: '请输入分类名称', trigger: 'blur' }
      ],
      icon: [
        { required: true, message: '请选择分类图标', trigger: 'change' }
      ],
      color: [
        { required: true, message: '请选择分类颜色', trigger: 'change' }
      ]
    }

    // 可用图标
    const availableIcons = [
      'Food', 'Car', 'ShoppingBag', 'VideoPlay', 'Medicine', 'Reading',
      'House', 'Phone', 'Money', 'TrendCharts', 'Briefcase', 'Gift'
    ]

    // 可用颜色
    const availableColors = [
      '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3',
      '#54a0ff', '#5f27cd', '#00d2d3', '#ff9f43', '#10ac84', '#ee5a24'
    ]

    // 支出分类数据
    const expenseCategories = reactive([
      {
        id: 1,
        name: '餐饮',
        description: '日常用餐、外卖、聚餐等',
        icon: 'Food',
        color: '#ff6b6b',
        enabled: true,
        monthlyCount: 45,
        totalAmount: 4200
      },
      {
        id: 2,
        name: '交通',
        description: '公交、地铁、打车、加油等',
        icon: 'Car',
        color: '#4ecdc4',
        enabled: true,
        monthlyCount: 28,
        totalAmount: 1800
      },
      {
        id: 3,
        name: '购物',
        description: '日用品、服装、电子产品等',
        icon: 'ShoppingBag',
        color: '#45b7d1',
        enabled: true,
        monthlyCount: 22,
        totalAmount: 3200
      },
      {
        id: 4,
        name: '娱乐',
        description: '电影、游戏、旅游等',
        icon: 'VideoPlay',
        color: '#96ceb4',
        enabled: true,
        monthlyCount: 18,
        totalAmount: 1200
      },
      {
        id: 5,
        name: '医疗',
        description: '看病、买药、体检等',
        icon: 'Medicine',
        color: '#feca57',
        enabled: true,
        monthlyCount: 6,
        totalAmount: 800
      },
      {
        id: 6,
        name: '教育',
        description: '学费、培训、书籍等',
        icon: 'Reading',
        color: '#ff9ff3',
        enabled: true,
        monthlyCount: 12,
        totalAmount: 1500
      },
      {
        id: 7,
        name: '住房',
        description: '房租、物业、水电等',
        icon: 'House',
        color: '#54a0ff',
        enabled: true,
        monthlyCount: 3,
        totalAmount: 3500
      },
      {
        id: 8,
        name: '通讯',
        description: '话费、网费、流量等',
        icon: 'Phone',
        color: '#5f27cd',
        enabled: true,
        monthlyCount: 4,
        totalAmount: 300
      }
    ])

    // 收入分类数据
    const incomeCategories = reactive([
      {
        id: 9,
        name: '工资',
        description: '基本工资、奖金、津贴等',
        icon: 'Money',
        color: '#52c41a',
        enabled: true,
        monthlyCount: 2,
        totalAmount: 21500
      },
      {
        id: 10,
        name: '兼职',
        description: '兼职收入、外快等',
        icon: 'Briefcase',
        color: '#1890ff',
        enabled: true,
        monthlyCount: 8,
        totalAmount: 2800
      },
      {
        id: 11,
        name: '投资',
        description: '股票、基金、理财收益等',
        icon: 'TrendCharts',
        color: '#722ed1',
        enabled: true,
        monthlyCount: 5,
        totalAmount: 1500
      },
      {
        id: 12,
        name: '礼金',
        description: '红包、礼金、奖励等',
        icon: 'Gift',
        color: '#eb2f96',
        enabled: true,
        monthlyCount: 3,
        totalAmount: 800
      }
    ])

    return {
      activeTab,
      showAddDialog,
      editingCategory,
      saving,
      categoryFormRef,
      categoryForm,
      categoryRules,
      availableIcons,
      availableColors,
      expenseCategories,
      incomeCategories
    }
  },
  methods: {
    // 处理分类操作
    handleCategoryAction(command) {
      const { action, category } = command

      switch (action) {
        case 'edit':
          this.editCategory(category)
          break
        case 'toggle':
          this.toggleCategory(category)
          break
        case 'delete':
          this.deleteCategory(category)
          break
      }
    },

    // 编辑分类
    editCategory(category) {
      this.editingCategory = category
      Object.assign(this.categoryForm, {
        type: category.id <= 8 ? 'expense' : 'income',
        name: category.name,
        description: category.description,
        icon: category.icon,
        color: category.color,
        enabled: category.enabled
      })
      this.showAddDialog = true
    },

    // 切换分类状态
    toggleCategory(category) {
      category.enabled = !category.enabled
      this.$message.success(`${category.name} 已${category.enabled ? '启用' : '禁用'}`)
    },

    // 删除分类
    deleteCategory(category) {
      if (category.monthlyCount > 0) {
        this.$message.warning('该分类本月有使用记录，无法删除')
        return
      }

      this.$confirm(`确定要删除分类 ${category.name} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const categories = category.id <= 8 ? this.expenseCategories : this.incomeCategories
        const index = categories.findIndex(c => c.id === category.id)
        if (index > -1) {
          categories.splice(index, 1)
          this.$message.success('删除成功')
        }
      }).catch(() => {})
    },

    // 保存分类
    async saveCategory() {
      try {
        const valid = await this.$refs.categoryFormRef.validate()
        if (!valid) return

        this.saving = true

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        if (this.editingCategory) {
          // 更新分类
          Object.assign(this.editingCategory, {
            name: this.categoryForm.name,
            description: this.categoryForm.description,
            icon: this.categoryForm.icon,
            color: this.categoryForm.color,
            enabled: this.categoryForm.enabled
          })
          this.$message.success('更新成功')
        } else {
          // 添加新分类
          const newCategory = {
            id: Date.now(),
            name: this.categoryForm.name,
            description: this.categoryForm.description,
            icon: this.categoryForm.icon,
            color: this.categoryForm.color,
            enabled: this.categoryForm.enabled,
            monthlyCount: 0,
            totalAmount: 0
          }

          if (this.categoryForm.type === 'expense') {
            this.expenseCategories.push(newCategory)
          } else {
            this.incomeCategories.push(newCategory)
          }
          this.$message.success('添加成功')
        }

        this.showAddDialog = false
        this.resetCategoryForm()
      } catch (error) {
        this.$message.error('操作失败，请重试')
      } finally {
        this.saving = false
      }
    },

    // 重置分类表单
    resetCategoryForm() {
      this.editingCategory = null
      Object.assign(this.categoryForm, {
        type: 'expense',
        name: '',
        description: '',
        icon: 'Food',
        color: '#ff6b6b',
        enabled: true
      })
      this.$refs.categoryFormRef?.clearValidate()
    },

    // 导出分类
    exportCategories() {
      this.$message.info('导出功能开发中...')
    }
  }
}
</script>

<style scoped>
.category-management {
  width: 100%;
  height: 100%;
  padding: 20px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  border-radius: 20px;
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 20px;
  height: calc(100vh - 120px);
  min-height: 500px;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 25px;
}

.page-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.title-icon {
  font-size: 32px;
  color: #409eff;
}

.page-subtitle {
  color: #7f8c8d;
  font-size: 14px;
  margin: 0;
}

/* 操作栏 */
.action-bar {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

/* 分类网格 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px 0;
}

.category-card {
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.category-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.category-card.disabled {
  opacity: 0.6;
  filter: grayscale(0.3);
}

.category-header {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 15px;
}

.category-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  flex-shrink: 0;
}

.category-info {
  flex: 1;
}

.category-name {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.category-desc {
  margin: 0;
  font-size: 14px;
  color: #7f8c8d;
  line-height: 1.4;
}

.category-actions {
  flex-shrink: 0;
}

.category-stats {
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #7f8c8d;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.amount-income {
  color: #52c41a;
}

.amount-expense {
  color: #ff4d4f;
}

/* 图标选择器 */
.icon-selector {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
  max-width: 300px;
}

.icon-option {
  width: 40px;
  height: 40px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 18px;
  color: #666;
}

.icon-option:hover {
  border-color: #409eff;
  background: rgba(64, 158, 255, 0.1);
}

.icon-option.selected {
  border-color: #409eff;
  background: #409eff;
  color: white;
}

/* 颜色选择器 */
.color-selector {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10px;
  max-width: 300px;
}

.color-option {
  width: 40px;
  height: 40px;
  border: 3px solid transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-option:hover {
  transform: scale(1.1);
}

.color-option.selected {
  border-color: #2c3e50;
  transform: scale(1.1);
}

/* Element Plus 组件自定义样式 */
:deep(.el-dialog) {
  border-radius: 16px;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  max-height: 90vh;
  overflow-y: auto;
}

:deep(.el-overlay) {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
}

:deep(.el-dialog__body) {
  padding: 10px 20px 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #2c3e50;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 8px;
}

:deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
}

:deep(.el-radio-button__inner) {
  border-radius: 8px;
}

:deep(.el-textarea__inner) {
  border-radius: 8px;
}

:deep(.el-tabs__header) {
  margin-bottom: 15px;
}

:deep(.el-tabs__nav-wrap::after) {
  background-color: rgba(0, 0, 0, 0.1);
}

:deep(.el-tabs__active-bar) {
  background-color: #409eff;
}

:deep(.el-tabs__item.is-active) {
  color: #409eff;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .category-management {
    padding: 15px;
  }

  .page-title {
    font-size: 24px;
  }

  .category-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .category-card {
    padding: 15px;
  }

  .icon-selector,
  .color-selector {
    grid-template-columns: repeat(4, 1fr);
  }

  :deep(.el-dialog) {
    width: 95% !important;
    margin: 0 !important;
    max-height: 85vh !important;
  }
}

@media (max-width: 480px) {
  .category-management {
    padding: 10px;
  }

  .page-title {
    font-size: 20px;
  }

  .category-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 10px;
  }

  .category-stats {
    flex-direction: column;
    gap: 10px;
  }

  .action-bar {
    flex-direction: column;
  }
}

/* 滚动条样式优化 */
.category-management::-webkit-scrollbar {
  width: 6px;
}

.category-management::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.category-management::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.category-management::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}
</style>
